import aiohttp
import asyncio
import re
import hashlib
import time
import json
from urllib.parse import quote
from bs4 import BeautifulSoup
import ssl

# astrbot插件相关导入
from astrbot.api.event import filter, AstrMessageEvent
from astrbot.api.star import Context, Star, register
from astrbot.api import logger, AstrBotConfig

# 默认配置文件（如果用户没有配置则使用默认值）
DEFAULT_JD_API_CONFIG = {
  "app_key": "4363c9848d8a43c8b7b9e957d3adcbcf",
  "method": "jd.union.open.promotion.byunionid.get",
  "format_type": "json",
  "sign_method": "md5",
  "v": "1.0",
  "secret_key": "905878013e044d2ba3adeebb05338086",
  "union_id": "1000054340",
  "union_id2": "1002084276",
  "position_id": "3003477075"
}

# 全局配置变量
JD_API_CONFIG = DEFAULT_JD_API_CONFIG.copy()
KOULING_API_URL = "https://jd.zack.xin/api/jd/ulink.php"
ENABLE_PROCESSING = True
MONITOR_GROUPS = []  # 监控的群组ID列表
FORWARD_GROUPS = []  # 转发目标群组ID列表
ENABLE_GROUP_MONITOR = False  # 是否启用群组监控功能
# 移除了API请求功能相关配置
# 青龙面板API配置
QL_API_URL = "http://localhost:5700"  # 青龙面板地址
QL_CLIENT_ID = ""  # 青龙面板Client ID
QL_CLIENT_SECRET = ""  # 青龙面板Client Secret
QL_TOKEN = ""  # 青龙面板访问令牌
QL_TOKEN_EXPIRES = 0  # 令牌过期时间

# 正则表达式模式
JD_LINK_PATTERN = r'(https://(?:u|item|item\.m|jingfen)\.jd\.com/\S*)'
PRODEV_PATTERN = r'(https://(?:prodev\.m\.jd\.com|pro\.m\.jd\.com)/\S*)'
KOULING_PATTERN = r'(\d+:/[^\n￥]*￥[A-Za-z0-9]+￥)'  # 优化后的口令正则
SHORT_URL_PATTERN = r'(https?://3\.cn/\S+)'

# 口令API配置
JD_KOULING_API = {
  "url": "https://jd.zack.xin/api/jd/ulink.php",
  "headers": {
      "Host": "jd.zack.xin",
      "Connection": "keep-alive",
      "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
      "Accept": "application/json, text/plain, */*",
      "sec-ch-ua-platform": '"Windows"',
      "sec-ch-ua-mobile": "?0",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
      "Content-Type": "application/x-www-form-urlencoded",
      "Origin": "https://jd.zack.xin",
      "Sec-Fetch-Site": "same-origin",
      "Sec-Fetch-Mode": "cors",
      "Sec-Fetch-Dest": "empty",
      "Referer": "https://jd.zack.xin/jd/zhushou/",
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
      "Accept-Encoding": "gzip, deflate",
  }
}

# 创建 SSL 上下文以禁用 SSL 检查
ssl_context_no_verify = ssl.create_default_context()
ssl_context_no_verify.check_hostname = False
ssl_context_no_verify.verify_mode = ssl.CERT_NONE

# 全局会话变量
session_default = None  # 默认会话，启用 SSL 验证
session_no_ssl = None    # 禁用 SSL 验证的会话

def generate_sign(app_key, method, param_json, timestamp, secret_key):
  """
  生成签名
  """
  sorted_params = sorted([
      f"app_key{app_key}",
      f"formatjson",
      f"method{method}",
      f"param_json{param_json}",
      f"sign_methodmd5",
      f"timestamp{timestamp}",
      f"v1.0"
  ])
  param_string = ''.join(sorted_params)
  sign_string = f"{secret_key}{param_string}{secret_key}"
  md5 = hashlib.md5()
  md5.update(sign_string.encode('utf-8'))
  return md5.hexdigest().upper()

async def get_ql_token():
  """
  获取青龙面板访问令牌
  基于青龙面板OpenAPI标准实现
  """
  global QL_TOKEN, QL_TOKEN_EXPIRES

  # 检查现有令牌是否有效
  if QL_TOKEN and time.time() < QL_TOKEN_EXPIRES - 300:  # 提前5分钟刷新
      return QL_TOKEN

  try:
      # 使用GET方式获取token，符合青龙面板OpenAPI标准
      url = f"{QL_API_URL}/open/auth/token?client_id={QL_CLIENT_ID}&client_secret={QL_CLIENT_SECRET}"

      async with session_default.get(url) as response:
          if response.status == 200:
              result = await response.json()
              if result.get("code") == 200:
                  token_data = result["data"]
                  # 构造完整的Authorization头
                  QL_TOKEN = f"{token_data['token_type']} {token_data['token']}"
                  QL_TOKEN_EXPIRES = time.time() + token_data["expiration"]
                  logger.info("青龙面板令牌获取成功")
                  return QL_TOKEN
              else:
                  logger.error(f"青龙面板认证失败: {result.get('message', '未知错误')}")
          else:
              logger.error(f"青龙面板API请求失败: HTTP {response.status}")
              response_text = await response.text()
              logger.error(f"响应内容: {response_text[:200]}...")

  except Exception as e:
      logger.error(f"获取青龙面板令牌时发生错误: {e}")

  return None

async def read_jd_cookies():
  """
  从青龙面板API获取JD_COOKIE变量
  支持格式: pt_key=1;pt_pin1;&pt_key=2;pt_pin2;&pt_key=3;pt_pin3;
  """
  cookies = []

  # 检查青龙面板配置
  if not QL_CLIENT_ID or not QL_CLIENT_SECRET:
      logger.error("青龙面板配置不完整，请配置Client ID和Client Secret")
      return cookies

  try:
      # 获取访问令牌
      token = await get_ql_token()
      if not token:
          logger.error("无法获取青龙面板访问令牌，请检查Client ID和Secret是否正确")
          return cookies

      # 请求环境变量，使用标准的青龙面板OpenAPI格式
      headers = {
          "Authorization": token,  # token已经包含了Bearer前缀
          "Content-Type": "application/json"
      }

      # 使用searchValue参数来优化查询，只获取JD_COOKIE相关的变量
      logger.info(f"正在从青龙面板获取JD_COOKIE变量: {QL_API_URL}")
      async with session_default.get(f"{QL_API_URL}/open/envs?searchValue=JD_COOKIE", headers=headers) as response:
          if response.status == 200:
              result = await response.json()
              if result.get("code") == 200:
                  envs = result.get("data", [])
                  logger.info(f"青龙面板返回 {len(envs)} 个环境变量")

                  # 查找JD_COOKIE变量
                  jd_cookie_value = None
                  for env in envs:
                      if env.get("name") == "JD_COOKIE" and env.get("status") == 0:  # status=0表示启用
                          jd_cookie_value = env.get("value", "")
                          logger.info(f"找到JD_COOKIE变量，长度: {len(jd_cookie_value)} 字符")
                          break

                  if jd_cookie_value:
                      # 处理cookie分割
                      cookies = parse_jd_cookies(jd_cookie_value)
                      logger.info(f"成功从青龙面板解析到 {len(cookies)} 个cookie")

                      # 输出每个cookie的pt_pin用于调试
                      for i, cookie in enumerate(cookies, 1):
                          pt_pin = extract_pt_pin(cookie)
                          logger.info(f"  Cookie {i}: pt_pin={pt_pin}")
                  else:
                      logger.warning("在青龙面板中未找到启用的JD_COOKIE变量，请检查:")
                      logger.warning("1. 变量名是否为 JD_COOKIE")
                      logger.warning("2. 变量状态是否为启用")
                      logger.warning("3. 变量值是否不为空")
              else:
                  logger.error(f"青龙面板API返回错误: {result.get('message', '未知错误')}")
          else:
              logger.error(f"青龙面板环境变量API请求失败: HTTP {response.status}")
              response_text = await response.text()
              logger.error(f"响应内容: {response_text[:200]}...")

  except Exception as e:
      logger.error(f"从青龙面板读取JD_COOKIE时发生错误: {e}")

  return cookies

def parse_jd_cookies(cookie_string):
  """
  解析JD_COOKIE字符串，支持多种格式
  格式1: pt_key=1;pt_pin1;&pt_key=2;pt_pin2;&pt_key=3;pt_pin3;
  格式2: cookie1&cookie2&cookie3
  """
  cookies = []

  # 移除首尾的引号
  cookie_string = cookie_string.strip('\'"')

  # 检查是否是新格式 (包含pt_key和pt_pin)
  if 'pt_key=' in cookie_string and 'pt_pin' in cookie_string:
      # 新格式: pt_key=1;pt_pin1;&pt_key=2;pt_pin2;
      # 按&分割，然后重新组合每个完整的cookie
      parts = cookie_string.split('&')
      current_cookie = ""

      for part in parts:
          part = part.strip()
          if not part:
              continue

          # 如果包含pt_key，说明是新cookie的开始
          if 'pt_key=' in part:
              # 保存之前的cookie
              if current_cookie:
                  cookies.append(current_cookie.strip(';'))
              current_cookie = part
          else:
              # 继续拼接当前cookie
              if current_cookie:
                  current_cookie += ";" + part
              else:
                  current_cookie = part

      # 添加最后一个cookie
      if current_cookie:
          cookies.append(current_cookie.strip(';'))

  else:
      # 传统格式: 直接按&分割
      cookies = [cookie.strip() for cookie in cookie_string.split('&') if cookie.strip()]

  # 过滤掉无效的cookie (必须包含pt_pin或能提取到用户名)
  valid_cookies = []
  for cookie in cookies:
      # 检查是否包含pt_pin或者能提取到有效的用户名
      if 'pt_pin' in cookie or ('pt_key=' in cookie and len(cookie.split(';')) >= 2):
          valid_cookies.append(cookie)
      else:
          logger.warning(f"跳过无效cookie (无法提取用户信息): {cookie[:50]}...")

  return valid_cookies

def extract_pt_pin(cookie):
  """
  从cookie中提取pt_pin
  支持多种格式:
  - pt_pin=username;
  - pt_key=1;pt_pin1; (pt_pin1就是用户名)
  """
  import re

  # 首先尝试标准格式 pt_pin=value
  match = re.search(r'pt_pin=([^;]+)', cookie)
  if match:
      return match.group(1)

  # 尝试新格式，查找pt_key后面的pt_pin值
  # 格式: pt_key=1;pt_pin1; 其中pt_pin1是用户名
  parts = cookie.split(';')
  pt_key_found = False

  for part in parts:
      part = part.strip()
      if part.startswith('pt_key='):
          pt_key_found = True
          continue

      # 如果找到了pt_key，下一个非空部分就是pt_pin
      if pt_key_found and part and not part.startswith('pt_'):
          return part

      # 如果是pt_pin开头但不是pt_pin=格式，直接返回
      if pt_key_found and part.startswith('pt_pin') and '=' not in part:
          return part

  return "未知"

# 移除了extract_activity_id函数

def find_key_recursive(data, target_key):
  """
  递归搜索嵌套字典中的指定键，并返回所有匹配的值。

  Args:
      data (dict or list): 要搜索的字典或列表。
      target_key (str): 目标键名。

  Returns:
      list: 所有匹配键的值列表。
  """
  results = []
  if isinstance(data, dict):
      for key, value in data.items():
          if key == target_key:
              results.append(value)
          elif isinstance(value, (dict, list)):
              results.extend(find_key_recursive(value, target_key))
  elif isinstance(data, list):
      for item in data:
          if isinstance(item, (dict, list)):
              results.extend(find_key_recursive(item, target_key))
  return results

async def call_jd_api(material_id, is_union_link):
  """
  调用京东API生成短链接
  """
  if is_union_link:
      param_json = f'{{"promotionCodeReq":{{"materialId":"{material_id}","unionId":"{JD_API_CONFIG["union_id"]}","positionId":"{JD_API_CONFIG["position_id"]}"}}}}'
  else:
      param_json = f'{{"promotionCodeReq":{{"materialId":"{material_id}","unionId":"{JD_API_CONFIG["union_id2"]}"}}}}'

  encoded_param_json = quote(param_json)
  timestamp = str(int(time.time()))
  sign = generate_sign(JD_API_CONFIG["app_key"], JD_API_CONFIG["method"], param_json, timestamp,
                       JD_API_CONFIG["secret_key"])

  data = (
      f"app_key={JD_API_CONFIG['app_key']}&"
      f"format={JD_API_CONFIG['format_type']}&"
      f"method={JD_API_CONFIG['method']}&"
      f"param_json={encoded_param_json}&"
      f"sign={sign}&"
      f"sign_method={JD_API_CONFIG['sign_method']}&"
      f"timestamp={timestamp}&"
      f"v={JD_API_CONFIG['v']}"
  )

  logger.info(f"请求参数: {data}")

  try:
      async with session_no_ssl.post("https://router.jd.com/api",
                                     headers={"Content-Type": "application/x-www-form-urlencoded"},
                                     data=data) as response:
          response_text = await response.text()
          logger.info(f"京东 API 响应: {response_text}")

          response_data = json.loads(response_text)
          jd_response = response_data.get('jd_union_open_promotion_byunionid_get_response', {})
          result = json.loads(jd_response.get('result', '{}'))

          if 'code' in result and result['code'] != 200:
              error_message = result.get('message', '未知错误')
              logger.error(f"京东 API 返回错误: {error_message}")
              return f"[链接生成失败: {error_message}]"

          short_url = result.get('data', {}).get('shortURL')
          if short_url:
              return short_url
          else:
              logger.error("响应中没有 shortURL")
              return "[链接生成失败: 未找到短链接]"
  except Exception as e:
      logger.error(f"调用京东 API 时出错: {e}")
      return f"[链接生成失败: {str(e)}]"

async def process_prodev_link(url):
  """
  处理 Prodev 链接，提取优惠券信息
  """
  try:
      logger.info(f"访问 Prodev 链接: {url}")
      headers = {
          'User-Agent': (''),
          "Cookie": 'pt_key=AAJnWIHXADBZ--Gcq7c1wzi2aQytEc1nF1lXkAdm8pizFns5ovodw1-mIVDPZQO_UFeZ0edT760;pt_pin=jd_4b25e12eb2177;'
      }
      # 使用禁用 SSL 验证的会话对象
      async with session_no_ssl.get(url, headers=headers, timeout=10) as response:
          if response.status != 200:
              logger.error(f"获取 Prodev 链接失败，状态码: {response.status}")
              return f"[提取 API 失败: 无法访问链接] {url}"
          html_content = await response.text()
          logger.debug("成功获取 Prodev 页面内容")

      # 提取活动 ID
      activity_id_match = re.search(r'/active/(\w+)/index\.html', url)
      activity_id = activity_id_match.group(1) if activity_id_match else '未找到 activity_id'
      logger.debug(f"活动 ID: {activity_id}")

      # 解析 HTML 内容
      soup = BeautifulSoup(html_content, 'html.parser')

      # 提取所有 <script> 标签中的 JSON 数据
      scripts = soup.find_all('script')
      api_data = None

      for script in scripts:
          if script.string:
              # 尝试解析整个脚本内容为 JSON
              try:
                  json_data = json.loads(script.string)
                  # 递归查找 'couponList'
                  coupon_lists = find_key_recursive(json_data, 'couponList')
                  if coupon_lists:
                      api_data = {'couponList': coupon_lists}
                      logger.debug("成功找到 couponList 在 JSON 脚本中")
                      break
              except json.JSONDecodeError:
                  # 如果脚本内容不是纯 JSON，尝试使用正则表达式提取
                  match = re.search(r'window\.__api_data__\s*=\s*(\{.*?\});', script.string, re.DOTALL)
                  if match:
                      json_text = match.group(1)
                      try:
                          json_text = re.sub(r';$', '', json_text)  # 移除结尾的分号
                          json_data = json.loads(json_text)
                          # 递归查找 'couponList'
                          coupon_lists = find_key_recursive(json_data, 'couponList')
                          if coupon_lists:
                              api_data = {'couponList': coupon_lists}
                              logger.debug("成功找到 couponList 在 window.__api_data__ 中")
                              break
                      except json.JSONDecodeError as e:
                          logger.error(f"解析 JSON 出错: {e}")
                          continue

      if not api_data:
          logger.error("在所有 <script> 标签中找不到 couponList。")
          return f"[提取 API 失败: 页面结构异常] {url}"

      # 提取 couponList
      coupon_list = api_data.get('couponList', [])
      if not coupon_list:
          logger.error("在 api_data 中找不到 couponList。")
          return f"[提取 API 失败: 无法获取优惠券列表] {url}"

      # 如果 couponList 是一个嵌套的列表，展平成单一列表
      flattened_coupon_list = []
      for cl in coupon_list:
          if isinstance(cl, list):
              flattened_coupon_list.extend(cl)
          else:
              flattened_coupon_list.append(cl)

      logger.debug(f"Extracted coupon_list: {json.dumps(flattened_coupon_list, ensure_ascii=False, indent=2)}")

      coupon_infos = []
      for idx, coupon in enumerate(flattened_coupon_list, start=1):
          if not isinstance(coupon, dict):
              logger.error(f"优惠券数据格式不正确，索引 {idx}，数据：{coupon}")
              continue

          # 尝试从顶层提取字段
          args_value = coupon.get('args', '')
          end_period_value = coupon.get('endPeriod', 'N/A')
          limit_str = coupon.get('limit', 'N/A').replace('可用', '')
          discount_str = coupon.get('discount', 'N/A')
          scope = coupon.get('scope', 'N/A')

          # 如果顶层字段缺失，尝试从 flexibleData 中提取
          if end_period_value == 'N/A':
              end_period_value = coupon.get('flexibleData', {}).get('endPeriod', 'N/A')
          if limit_str == 'N/A':
              limit_str = coupon.get('flexibleData', {}).get('limit', 'N/A').replace('可用', '')
          if discount_str == 'N/A':
              discount_str = coupon.get('flexibleData', {}).get('discount', 'N/A')
          if scope == 'N/A':
              scope = coupon.get('flexibleData', {}).get('scope', 'N/A')

          coupon_name = f"{limit_str}-{discount_str}"
          body_dict = {
              "activityId": activity_id,
              "scene": "1",
              "args": args_value
          }
          body = quote(json.dumps(body_dict, separators=(',', ':')))
          api_url = f'https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body={body}'

          logger.debug(f"构建 API URL: {api_url}")

          coupon_info = (
              f"#{idx}\n"
              f"优惠券名称: {coupon_name}\n"
              f"可用范围: {scope}\n"
              f"活动有效期: {end_period_value}\n"
              f"API 地址为:\n{api_url}\n"
          )
          coupon_infos.append(coupon_info)

      logger.info(f"成功提取 {len(coupon_infos)} 个优惠券信息")
      return "\n".join(coupon_infos)

  except asyncio.TimeoutError:
      logger.error(f"处理 Prodev 链接超时: {url}")
      return f"[提取 API 失败: 请求超时] {url}"
  except aiohttp.ClientError as e:
      logger.error(f"处理 Prodev 链接时发生网络错误: {str(e)}")
      return f"[提取 API 失败: 网络错误] {url}"
  except Exception as e:
      logger.error(f"处理 Prodev 链接时发生未知错误: {str(e)}")
      return f"[提取 API 失败: 未知错误] {url}"

async def process_kouling(kouling):
  """
  处理口令，调用口令API解析
  """
  data = {
      "url": kouling,
      "type": "kl",
      "u": "jApp",
      "model": "json"
  }
  try:
      async with session_default.post(KOULING_API_URL, headers=JD_KOULING_API["headers"], data=data) as response:
          content_type = response.headers.get('Content-Type', '').lower()
          if 'application/json' in content_type:
              response_json = await response.json()
          else:
              response_text = await response.text()
              response_json = json.loads(response_text)

          if response_json.get("status") == 200:
              code = response_json.get("code", "")
              title_match = re.search(r'【标题】\n(.*?)\n', code)
              link_match = re.search(r'【链接】\n(.*?)$', code, re.MULTILINE)

              if title_match and link_match:
                  return f"{title_match.group(1)}\n{link_match.group(1)}"
              else:
                  return kouling  # 如果无法提取标题和链接，返回原始口令
          else:
              logger.error(f"处理口令失败: {response_json.get('msg', '未知错误')}")
              return kouling
  except Exception as e:
      logger.error(f"处理口令时发生错误: {str(e)}")
      return kouling

def split_message(text, max_length=4096):
  """
  将长消息拆分为多个不超过 max_length 的部分
  """
  if len(text) <= max_length:
      return [text]

  lines = text.split('\n')
  messages = []
  current_message = ""

  for line in lines:
      # +1 是因为每行后面会加一个换行符
      if len(current_message) + len(line) + 1 > max_length:
          messages.append(current_message)
          current_message = line + '\n'
      else:
          current_message += line + '\n'

  if current_message:
      messages.append(current_message)

  return messages

async def short_to_real(short_url):
    """
    解析3.cn的短链接，获取真实的长链接

    Args:
        short_url (str): 短链接

    Returns:
        str: 真实的长链接或错误信息
    """
    logger.info(f"尝试解析短链接: {short_url}")
    try:
        # 使用禁用 SSL 验证的会话对象
        async with session_no_ssl.get(short_url, allow_redirects=True, timeout=10) as response:
            resolved_url = str(response.url)
            logger.info(f"解析成功: {short_url} -> {resolved_url}")
            return resolved_url
    except asyncio.TimeoutError:
        logger.error(f"解析短链接超时: {short_url}")
        return f"[解析失败: 请求超时]"
    except aiohttp.ClientError as e:
        logger.error(f"解析短链接时发生网络错误: {short_url}, 错误: {e}")
        return f"[解析失败: 网络错误]"
    except Exception as e:
        logger.error(f"解析短链接时发生未知错误: {short_url}, 错误: {e}")
        return f"[解析失败: 未知错误]"

# 移除了京东API请求处理函数

# 移除了京东API请求相关函数


async def process_message_content(message_text):
  """
  处理消息内容，解析链接并进行相应处理
  """
  logger.info(f"处理消息: {message_text}")

  message_lines = message_text.splitlines()
  new_message_lines = []

  for line in message_lines:
      # 处理京东链接
      jd_links = re.findall(JD_LINK_PATTERN, line)
      if jd_links:
          for link in jd_links:
              logger.info(f"提取到的京东链接: {link}")
              is_union_link = "u.jd.com" in link
              short_url = await call_jd_api(link, is_union_link)

              if short_url:
                  line = line.replace(link, short_url)
              else:
                  line = line.replace(link, f"[生成失败]({link})")

      # 处理Prodev链接
      prodev_links = re.findall(PRODEV_PATTERN, line)
      if prodev_links:
          for link in prodev_links:
              logger.info(f"提取到的Prodev链接: {link}")
              api_urls = await process_prodev_link(link)
              if api_urls:
                  new_message_lines.append(api_urls)
              else:
                  new_message_lines.append(f"[生成失败]({link})")
          continue  # 跳过添加此行，因为已添加处理后的Prodev链接

      # 处理口令
      kouling_matches = re.findall(KOULING_PATTERN, line)
      if kouling_matches:
          for kouling in kouling_matches:
              logger.info(f"提取到的口令: {kouling}")
              processed_result = await process_kouling(kouling)
              line = line.replace(kouling, processed_result)

      # 处理3.cn短链接
      short_urls = re.findall(SHORT_URL_PATTERN, line)
      if short_urls:
          for short_url in short_urls:
              logger.info(f"提取到的短链接: {short_url}")
              resolved_url = await short_to_real(short_url)
              if resolved_url and resolved_url.startswith("http"):
                  line = line.replace(short_url, resolved_url)
              else:
                  line = line.replace(short_url, f"[解析失败]({short_url})")

      # 移除了京东API链接处理

      new_message_lines.append(line)

  new_message = "\n".join(new_message_lines)
  return new_message if new_message != message_text else None

# astrbot插件类
@register("JD", "Z", "JD", "1.0.0", "https://www.baidu.com")
class XianbaoProcessor(Star):
    def __init__(self, context: Context, config: AstrBotConfig = None):
        super().__init__(context)
        self.config = config or {}

        # 更新全局配置
        self.update_global_config()

        # 初始化会话
        asyncio.create_task(self.initialize_sessions())

    def update_global_config(self):
        """根据用户配置更新全局配置变量"""
        global JD_API_CONFIG, KOULING_API_URL, ENABLE_PROCESSING, MONITOR_GROUPS, FORWARD_GROUPS, ENABLE_GROUP_MONITOR
        global QL_API_URL, QL_CLIENT_ID, QL_CLIENT_SECRET

        if self.config:
            # 更新京东API配置
            if "jd_api" in self.config:
                jd_config = self.config["jd_api"]
                JD_API_CONFIG.update({
                    "app_key": jd_config.get("app_key", DEFAULT_JD_API_CONFIG["app_key"]),
                    "secret_key": jd_config.get("secret_key", DEFAULT_JD_API_CONFIG["secret_key"]),
                    "union_id": jd_config.get("union_id", DEFAULT_JD_API_CONFIG["union_id"]),
                    "union_id2": jd_config.get("union_id2", DEFAULT_JD_API_CONFIG["union_id2"]),
                    "position_id": jd_config.get("position_id", DEFAULT_JD_API_CONFIG["position_id"])
                })

            # 更新口令API配置
            if "kouling_api" in self.config:
                KOULING_API_URL = self.config["kouling_api"].get("url", KOULING_API_URL)

            # 移除了京东API请求配置

            # 更新青龙面板配置
            if "qinglong_config" in self.config:
                ql_config = self.config["qinglong_config"]
                QL_API_URL = ql_config.get("api_url", QL_API_URL)
                QL_CLIENT_ID = ql_config.get("client_id", QL_CLIENT_ID)
                QL_CLIENT_SECRET = ql_config.get("client_secret", QL_CLIENT_SECRET)

            # 更新处理开关
            ENABLE_PROCESSING = self.config.get("enable_processing", True)

            # 更新群组监控配置
            if "group_monitor" in self.config:
                group_config = self.config["group_monitor"]
                ENABLE_GROUP_MONITOR = group_config.get("enable", False)
                MONITOR_GROUPS = group_config.get("monitor_groups", [])
                FORWARD_GROUPS = group_config.get("forward_groups", [])
                self.forward_to_current = group_config.get("forward_to_current", True)
            else:
                self.forward_to_current = True

        logger.info(f"配置已更新: 处理功能{'启用' if ENABLE_PROCESSING else '禁用'}, 群组监控{'启用' if ENABLE_GROUP_MONITOR else '禁用'}")

    async def initialize_sessions(self):
        """初始化全局 aiohttp 会话"""
        global session_default, session_no_ssl
        if session_default is None:
            session_default = aiohttp.ClientSession()
        if session_no_ssl is None:
            session_no_ssl = aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context_no_verify))
        logger.info("会话初始化完成")

    @filter.event_message_type(filter.EventMessageType.ALL)
    async def on_message(self, event: AstrMessageEvent):
        """处理所有消息，检测并处理线报链接"""
        # 检查是否启用处理功能
        if not ENABLE_PROCESSING:
            return

        message_text = event.message_str
        current_group_id = event.get_group_id()

        # 检查是否需要进行群组监控
        should_process = False

        if ENABLE_GROUP_MONITOR and MONITOR_GROUPS:
            # 如果启用了群组监控，只处理监控群组的消息
            if current_group_id and current_group_id in MONITOR_GROUPS:
                should_process = True
                logger.info(f"检测到监控群组 {current_group_id} 的消息")
        else:
            # 如果没有启用群组监控，处理所有消息
            should_process = True

        if not should_process:
            return

        # 检查消息是否包含需要处理的链接
        if not (re.search(JD_LINK_PATTERN, message_text) or
                re.search(PRODEV_PATTERN, message_text) or
                re.search(KOULING_PATTERN, message_text) or
                re.search(SHORT_URL_PATTERN, message_text)):
            return  # 没有需要处理的链接，直接返回

        try:
            # 处理消息内容
            processed_message = await process_message_content(message_text)

            if processed_message:
                # 分割长消息
                split_messages = split_message(processed_message)

                # 如果启用了群组监控且配置了转发群组
                if ENABLE_GROUP_MONITOR and FORWARD_GROUPS:
                    # 转发到配置的目标群组
                    for target_group_id in FORWARD_GROUPS:
                        for msg in split_messages:
                            try:
                                # 尝试不同的unified_msg_origin格式
                                possible_origins = [
                                    f"group_{target_group_id}",
                                    f"{event.get_platform_name()}_group_{target_group_id}",
                                    target_group_id
                                ]

                                success = False
                                for target_origin in possible_origins:
                                    try:
                                        await self.context.send_message(target_origin, msg)
                                        logger.info(f"消息已转发到群组 {target_group_id} (使用格式: {target_origin})")
                                        success = True
                                        break
                                    except Exception as e:
                                        logger.debug(f"使用格式 {target_origin} 转发失败: {e}")
                                        continue

                                if not success:
                                    logger.error(f"所有格式都无法转发到群组 {target_group_id}")

                            except Exception as e:
                                logger.error(f"转发消息到群组 {target_group_id} 失败: {e}")

                # 如果配置了转发到当前群组，或者没有启用群组监控
                if (not ENABLE_GROUP_MONITOR) or (ENABLE_GROUP_MONITOR and self.forward_to_current):
                    for msg in split_messages:
                        yield event.plain_result(msg)

        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}")
            yield event.plain_result(f"处理消息时发生错误: {str(e)}")

    @filter.command("jd")
    async def jd_command(self, event: AstrMessageEvent, action: str = "status"):
        """京东线报处理插件控制指令"""
        global ENABLE_PROCESSING

        if action == "status":
            status = "启用" if ENABLE_PROCESSING else "禁用"
            monitor_status = "启用" if ENABLE_GROUP_MONITOR else "禁用"
            forward_to_current_status = "是" if self.forward_to_current else "否"

            # 检查cookie状态 (异步调用)
            try:
                cookies = await read_jd_cookies()
                cookie_count = len(cookies)
            except Exception as e:
                logger.error(f"获取cookie状态失败: {e}")
                cookie_count = 0

            status_text = f"""京东线报处理功能状态:
处理功能: {status}
Cookie数量: {cookie_count} 个
青龙面板: {QL_API_URL}
群组监控: {monitor_status}
监控群组: {len(MONITOR_GROUPS)} 个 {MONITOR_GROUPS if MONITOR_GROUPS else ''}
转发群组: {len(FORWARD_GROUPS)} 个 {FORWARD_GROUPS if FORWARD_GROUPS else ''}
转发到当前群组: {forward_to_current_status}"""
            yield event.plain_result(status_text)

        elif action == "enable":
            ENABLE_PROCESSING = True
            yield event.plain_result("京东线报处理功能已启用")
        elif action == "disable":
            ENABLE_PROCESSING = False
            yield event.plain_result("京东线报处理功能已禁用")
        elif action == "help":
            help_text = """京东线报处理插件使用说明:
/jd status - 查看当前状态
/jd enable - 启用处理功能
/jd disable - 禁用处理功能
/jd help - 显示帮助信息

支持的链接类型:
- 京东商品链接 (item.jd.com, u.jd.com等)
- Prodev活动链接 (prodev.m.jd.com, pro.m.jd.com)
- 京东口令 (格式: 数字:/￥...￥)
- 3.cn短链接

群组监控功能:
- 可以监控指定群组的消息
- 自动转发处理后的结果到目标群组
- 在配置文件中设置监控和转发群组ID

配置说明:
- 青龙面板API: 通过青龙面板API获取JD_COOKIE变量
- 必须配置青龙面板Client ID和Secret
- 变量名固定为: JD_COOKIE"""
            yield event.plain_result(help_text)
        else:
            yield event.plain_result("未知操作，使用 /jd help 查看帮助")

    async def terminate(self):
        """插件卸载时关闭会话"""
        await self.close_sessions()

    async def close_sessions(self):
        """关闭全局 aiohttp 会话"""
        global session_default, session_no_ssl
        if session_default:
            await session_default.close()
            session_default = None
            logger.info("已关闭 session_default")
        if session_no_ssl:
            await session_no_ssl.close()
            session_no_ssl = None
            logger.info("已关闭 session_no_ssl")

# 插件入口点 - astrbot会自动加载插件类，不需要main函数