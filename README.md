# 线报链接处理器 AstrBot 插件

这是一个适用于 AstrBot 的线报链接处理插件，可以自动处理各种线报链接并生成相应的推广链接或解析结果。

## 功能特性

- 🔗 **京东链接处理**: 自动将京东商品链接转换为推广短链接
- 🎫 **Prodev链接解析**: 解析京东Prodev活动页面，提取优惠券信息和API地址
- 💬 **口令解析**: 解析京东口令，获取商品标题和链接
- 🔗 **短链接解析**: 解析3.cn等短链接，获取真实地址
- ⚙️ **配置管理**: 支持通过AstrBot管理面板进行配置
- 🎛️ **开关控制**: 可以通过指令启用/禁用处理功能
- 👥 **群组监控**: 可以监控指定群组的消息并转发处理结果到目标群组

## 支持的链接类型

1. **京东商品链接**
   - `item.jd.com`
   - `item.m.jd.com`
   - `u.jd.com`
   - `jingfen.jd.com`

2. **Prodev活动链接**
   - `prodev.m.jd.com`
   - `pro.m.jd.com`

3. **京东口令**
   - 格式: `数字:/￥...￥`

4. **短链接**
   - `3.cn` 短链接

## 安装方法

1. 将插件文件放置到 AstrBot 的插件目录中
2. 重启 AstrBot 或在管理面板中重载插件
3. 在管理面板中配置插件参数（可选）

## 配置说明

插件支持以下配置项：

### 京东API配置
- `app_key`: 京东联盟APP KEY
- `secret_key`: 京东联盟SECRET KEY  
- `union_id`: 联盟ID
- `union_id2`: 备用联盟ID
- `position_id`: 推广位ID

### 口令API配置
- `url`: 口令解析API地址

### 功能开关
- `enable_processing`: 是否启用自动处理功能

### 青龙面板配置
- `api_url`: 青龙面板地址 (默认: http://localhost:5700)
- `client_id`: 青龙面板Client ID
- `client_secret`: 青龙面板Client Secret

### 群组监控配置
- `enable`: 是否启用群组监控功能
- `monitor_groups`: 要监控的群组ID列表
- `forward_groups`: 处理后消息转发到的群组ID列表
- `forward_to_current`: 是否将处理结果转发到当前接收消息的群组

## 使用方法

### 自动处理
插件会自动监听所有消息，当检测到支持的链接类型时，会自动进行处理并回复处理结果。

### 手动控制指令

- `/jd status` - 查看当前状态
- `/jd enable` - 启用处理功能
- `/jd disable` - 禁用处理功能
- `/jd help` - 显示帮助信息

### 群组监控功能

当启用群组监控功能时：
1. **监控模式**: 只处理配置的监控群组中的消息
2. **转发功能**: 将处理后的结果转发到指定的目标群组
3. **灵活配置**: 可以选择是否同时在当前群组回复

**配置示例**:
```json
{
  "group_monitor": {
    "enable": true,
    "monitor_groups": ["123456789", "987654321"],
    "forward_groups": ["111111111", "222222222"],
    "forward_to_current": false
  }
}
```

**使用场景**:
- 监控线报群组，自动转发处理后的链接到自己的群组
- 多群组同步，一个群组的线报自动分发到多个群组
- 过滤和处理，只转发有效的线报信息

## 处理示例

### 京东链接处理
输入: `https://item.jd.com/12345678.html`
输出: `https://u.jd.com/shortlink`

### Prodev链接处理
输入: `https://prodev.m.jd.com/mall/active/xxx/index.html`
输出: 
```
#1
优惠券名称: 满100-10
可用范围: 全品类
活动有效期: 2024-12-31
API 地址为:
https://api.m.jd.com/client.action?functionId=...
```

### 口令处理
输入: `1:/￥ABC123￥`
输出:
```
商品标题
https://item.jd.com/...
```

### Cookie格式支持
插件支持多种cookie格式，在青龙面板环境变量中配置：

**新格式** (推荐):
```
变量名: JD_COOKIE
变量值: pt_key=1;pt_pin1;&pt_key=2;pt_pin2;&pt_key=3;pt_pin3;
```

**传统格式**:
```
变量名: JD_COOKIE
变量值: pt_pin=user1;pt_key=abc123;&pt_pin=user2;pt_key=def456;
```

## 注意事项

1. 需要有效的京东联盟账号和API密钥
2. 部分功能需要网络连接
3. 处理大量链接时可能会有延迟
4. 建议定期检查API配置的有效性
5. **青龙面板配置**: 必须在青龙面板中创建应用获取Client ID和Secret
6. **JD_COOKIE变量**: 必须在青龙面板环境变量中配置名为JD_COOKIE的变量
7. **多账号**: 支持多个京东账号，cookie之间用&分割
8. **网络访问**: 确保插件能够访问青龙面板API接口
9. **变量状态**: 确保JD_COOKIE变量在青龙面板中为启用状态

## 更新日志

### v1.0.0
- 初始版本
- 支持京东链接、Prodev链接、口令和短链接处理
- 去除Telegram推送功能，适配AstrBot
- 添加配置管理和开关控制功能
- 指令从 `/xianbao` 改为 `/jd`
- 新增群组监控和转发功能
- 支持监控指定群组并转发处理结果到目标群组
- **集成青龙面板API获取JD_COOKIE**
- 支持青龙面板应用认证和令牌管理
- 移除了API抢券请求功能，专注于链接处理

## 技术支持

如有问题或建议，请联系插件作者或在相关社区提出。
